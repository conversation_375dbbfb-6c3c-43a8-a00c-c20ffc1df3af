package com.tencent.andata.conf.manager.struct;

public class InfraConf {
    public NRTConf nrtConf; // L1 ODS 近实时分发配置
    public MQDistributeConf l1OdsMQDistributeConf; // L1 ODS数据总线配置
    public MQDistributeConf l2OdsMQDistributeConf; // L2 ODS数据总线配置
    public MQDistributeConf internalDBusDistributedConf; // 内部数据总线配置
    public ReportPlatformConf reportPlatformConf; // 接入平台配置

    @Override
    public String toString() {
        return "InfraConf{" +
                "nrtConf=" + nrtConf +
                ", internalDBusDistributedConf=" + internalDBusDistributedConf +
                '}';
    }
}