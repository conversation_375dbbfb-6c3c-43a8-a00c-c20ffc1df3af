package com.tencent.andata.dispatcher.rengine.lookup.builder;

import com.tencent.andata.dispatcher.rengine.lookup.interfaces.AntoolQuery;
import com.tencent.andata.dispatcher.rengine.lookup.query.AntoolMultipleTableJoinLookupQuery;
import com.tencent.andata.dispatcher.rengine.lookup.interfaces.FieldConverter;
import com.tencent.andata.dispatcher.rengine.lookup.query.SingleTableQuery;
import com.tencent.andata.utils.lookup.jdbc.HashMapJDBCLookupQuery;
import com.tencent.andata.utils.lookup.jdbc.JDBCSqlBuilderImpl;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.joni.exception.ValueException;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

public class MultipleTableJoinLookupQueryBuilder {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static AntoolMultipleTableJoinLookupQuery buildFromJson(String primaryTableName, String jsonData) throws Exception {
        List<Tuple2<AntoolQuery, Integer>> subTableQueryList = new ArrayList<>();
        // 解析json
        // 获取关联数据
        final JsonNode join = objectMapper.readValue(jsonData, JsonNode.class);
        // 关联数据是一个列表
        final Iterator<JsonNode> joinIterator = join.iterator();
        int cnt = 0;
        // 遍历多个DB
        while (joinIterator.hasNext()) {
            final JsonNode joinData = joinIterator.next();
            // 解析DB数据
            final DatabaseEnum dbEnum = getDatabaseEnum(joinData.get("database"));
            final DatabaseConf dbConf = buildDatabaseConf(joinData.get("database"));
            // 解析关联表数据
            final JsonNode tableList = joinData.get("table");
            final Iterator<JsonNode> tableIterator = tableList.iterator();
            // 遍历所有表 一个表一个Query
            while (tableIterator.hasNext()) {
                final JsonNode table = tableIterator.next();
                Integer level;
                // 从配置中获取Level层级，进行排序，这是Query的查询顺序
                // 如果没有level，则默认按照Json顺序排序
                try {
                    level = table.get("level").intValue();
                } catch (Exception e) {
                    level = cnt;
                }
                // 获取sql builder和on字段映射
                Tuple2<JDBCSqlBuilderImpl, HashMap<String, Tuple2<String, FieldConverter>>> dataTuple = getSqlBuilderFromJson(
                        table, dbEnum
                );
                // 生成Query
                final JDBCSqlBuilderImpl sqlBuilder = dataTuple.f0;
                final HashMap<String, Tuple2<String, FieldConverter>> fieldMapAndConverter = dataTuple.f1;
                final HashMapJDBCLookupQuery hashMapJDBCLookupQuery = new HashMapJDBCLookupQuery(
                        dbEnum,
                        dbConf,
                        dataTuple.f0
                );
                // 加到列表
                subTableQueryList.add(
                        new Tuple2<>(
                                new SingleTableQuery(
                                        hashMapJDBCLookupQuery,
                                        fieldMapAndConverter,
                                        (HashMap<String, String>) sqlBuilder.getSelectFieldWithAlias()
                                ),
                                level
                        )
                );
                cnt += 1;
            }
        }
        // 根据Level对Query排序
        subTableQueryList.sort(
                new Comparator<Tuple2<AntoolQuery, Integer>>() {
                    @Override
                    public int compare(Tuple2<AntoolQuery, Integer> o1,
                                       Tuple2<AntoolQuery, Integer> o2) {
                        return o1.f1 - o2.f1;
                    }
                }
        );
        return new AntoolMultipleTableJoinLookupQuery(
                primaryTableName,
                subTableQueryList.stream().map(f -> f.f0).collect(Collectors.toList())
        );
    }


    /**
     * 从table参数中获取sql builder和on condition的字段映射
     *
     * @param tableJson table json
     * @return tuple2
     */
    public static Tuple2<JDBCSqlBuilderImpl, HashMap<String, Tuple2<String, FieldConverter>>> getSqlBuilderFromJson(JsonNode tableJson, DatabaseEnum databaseEnum) {
        final String tableName = tableJson.get("tableName").textValue();
        final HashMap<String, String> fieldMap = new HashMap<>();
        final List<String> conditionKeyList = new ArrayList<>();
        final HashMap<String, Tuple2<String, FieldConverter>> onFieldMap = new HashMap<>();
        // 获取字段名映射
        final Iterator<JsonNode> fieldMappingIterator = tableJson.get("mapping").iterator();
        while (fieldMappingIterator.hasNext()) {
            final JsonNode fieldMapping = fieldMappingIterator.next();
            // 提取出字段名的映射，注意这里是去查询子表，所以key是子表的字段名
            // 用于生成select field as primaryField 这样的sql
            fieldMap.put(fieldMapping.get("field").textValue(), fieldMapping.get("primaryField").textValue());
        }
        // 获取Condition条件
        final Iterator<JsonNode> conditionIterator = tableJson.get("on").iterator();
        while (conditionIterator.hasNext()) {
            final JsonNode condition = conditionIterator.next();
            // 将子表的字段添加到sql builder里去
            conditionKeyList.add(condition.get("field").textValue());

            // 生成converter, 可能主表的ticket_id是bigint，子表的ticket_id是string，所以拼接sql之前需要将数据做一次转换
            final FieldConverter fieldConverter = buildConverter(
                    condition.get("primaryFieldType").textValue(),
                    condition.get("fieldType").textValue()
            );

            // 维护on条件的主表字段和子表字段map，下游转换的时候需要用到
            onFieldMap.put(
                    condition.get("field").textValue(),
                    new Tuple2<>(
                            condition.get("primaryField").textValue(),
                            fieldConverter
                    )
            );

        }
        // 构造Builder返回
        return new Tuple2<>(
                JDBCSqlBuilderImpl.builder()
                        .tableName(tableName)
                        .selectFieldWithAlias(fieldMap)
                        .conditionKeyList(conditionKeyList)
                        .databaseEnum(databaseEnum)
                        // TODO 本次多表关联场景暂时没对数量限制，后续通过协议限制数量
                        .limit(1),
                onFieldMap
        );
    }

    /**
     * 主表和字段的关联字段的类型可能不一致，这里需要做一层Converter
     * 上游antool做了限制，这里fieldType只有number和string
     *
     * @param primaryFieldType 主表字段类型
     * @param fieldType        子表字段类型
     * @return converter
     */
    public static FieldConverter buildConverter(String primaryFieldType, String fieldType) {
        if (fieldType.equals("number") && primaryFieldType.equals("string")) {
            return new FieldConverter() {
                @Override
                public Object convert(Object in) {
                    return Integer.valueOf((String) in);
                }
            };
        }
        if (fieldType.equals("string") && primaryFieldType.equals("number")) {
            return new FieldConverter() {
                @Override
                public Object convert(Object in) {
                    return String.valueOf((int) in);
                }
            };
        }
        return new FieldConverter() {
            @Override
            public Object convert(Object in) {
                return in;
            }
        };
    }

    /**
     * 从JSON中解析DB数据
     *
     * @param dbData DB json
     * @return db conf
     */
    public static DatabaseConf buildDatabaseConf(JsonNode dbData) {
        final DatabaseConf res = new DatabaseConf();
        res.dbHost = dbData.get("ip").textValue();
        res.dbName = dbData.get("database").textValue();
        res.dbPort = Integer.valueOf(dbData.get("port").textValue());
        res.userName = dbData.get("username").textValue();
        res.password = dbData.get("password").textValue();
        return res;
    }

    /**
     * 从db中解析 db类型
     *
     * @param dbData db json
     * @return database enum
     * @throws ValueException ex
     */
    public static DatabaseEnum getDatabaseEnum(JsonNode dbData) throws ValueException {
        final String type = dbData.get("type").textValue();
        switch (type) {
            case "pgsql":
                return DatabaseEnum.PGSQL;
            case "mysql":
                return DatabaseEnum.MYSQL;
            default:
                throw new ValueException(String.format(
                        "no such type: %s",
                        type
                ));
        }
    }

}
