package com.tencent.andata.demand.consumers;

import com.tencent.andata.demand.consumers.strategy.DemandProcessFactory;
import com.tencent.andata.demand.consumers.strategy.DemandProcessStrategy;
import com.tencent.andata.demand.lookup.LevelLookUpQuery;
import com.tencent.andata.demand.lookup.RemarkLookupQuery;
import com.tencent.andata.demand.structs.ConsumerDemandConfig;
import com.tencent.andata.demand.udfs.*;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.udf.GetJsonObject;
import com.tencent.andata.utils.udf.LongTimestampTransform;
import com.tencent.andata.utils.udf.SimpleStringTransform;
import com.tencent.andata.utils.udf.SqlTimestampTransform;
import com.tencent.andata.utils.udf.StrToTimestamp;
import com.tencent.andata.utils.udf.StringDataCleanTransform;
import com.tencent.andata.utils.udf.StringTimestampTransform;
import com.tencent.andata.demand.lookup.HolidayLookupQuery;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.util.Properties;
import java.util.concurrent.TimeUnit;

public class ConsumerDemandManager {
    private static RainbowUtils rainbowUtils;

    public static void main(String[] args) throws Exception {
        // 初始化环境
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        // 导入Rainbow密钥和环境
        Properties properties = PropertyUtils.loadProperties("env.properties");
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        // rainbow初始化
        rainbowUtils = new RainbowUtils(properties);

        Configuration configuration = flinkEnv.streamTEnv().getConfig().getConfiguration();
        configuration.setString("table.exec.resource.default-parallelism", "1");
        configuration.setString("table.dynamic-table-options.enabled", "true");
        configuration.setString("execution-runtime-mode", "streaming");
        configuration.setString("table.exec.sink.not-null-enforcer", "DROP");
        configuration.setString("pipeline.name", "DMD&VOC");
        configuration.setString("table.exec.legacy-cast-behaviour", "enabled");
        configuration.setString("table.exec.state.ttl", "86400000");

        ConsumerDemandConfig config =
                new KVConfBuilder<>(ConsumerDemandConfig.class)
                        .setRainbowUtils(rainbowUtils)
                        .setGroupName("demand.conf")
                        .build();
        System.out.println(config);

        DatabaseConf pgDBConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "dataware"))
                .build();

        DatabaseConf starrocksConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "dataware"))
                .build();

        DatabaseConf mysqlDbConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName((config.cdcDatabasePath))
                .build();

        // 初始化UDF
        initUDF(flinkEnv, pgDBConf, mysqlDbConf,starrocksConf);

        // 获取subApplicationStr参数
        String subApplicationStr = parameterTool.get("subApplication");
        for (String app : subApplicationStr.split(",")) {
            // 获取相应的处理策略
            DemandProcessStrategy processStrategy = DemandProcessFactory.getProcess(app, config);
            // 执行处理
            processStrategy.run(flinkEnv, new IcebergCatalogReader(),parameterTool);
        }



        flinkEnv.env().enableCheckpointing(60 * 1000L, CheckpointingMode.AT_LEAST_ONCE);
        // 设置失败率重启策略
        flinkEnv.env().setRestartStrategy(RestartStrategies.failureRateRestart(
                50, // max failures per interval
                Time.of(2, TimeUnit.MINUTES), // time interval for measuring failure rate
                Time.of(10, TimeUnit.SECONDS) // delay
        ));
        flinkEnv.stmtSet().execute();
        flinkEnv.env().disableOperatorChaining();
        flinkEnv.env().execute();
    }

    /**
     * 初始化UDF
     *
     * @param flinkEnv flink Env
     */
    public static void initUDF(FlinkEnvUtils.FlinkEnv flinkEnv,DatabaseConf pgDBConf,DatabaseConf mysqlDbConf,DatabaseConf starrocksConf) throws Exception {
        StreamTableEnvironment tableEnv = flinkEnv.streamTEnv();
        RemarkLookupQuery remarkLookupQuery = new RemarkLookupQuery(mysqlDbConf);
        LevelLookUpQuery levelLookUpQuery = new LevelLookUpQuery(pgDBConf);
        // 初始化节假日查询，使用StarRocks数据库配置（通过MySQL协议）
        HolidayLookupQuery holidayLookupQuery = new HolidayLookupQuery(starrocksConf);
        
        remarkLookupQuery.open();
        levelLookUpQuery.open();
        holidayLookupQuery.open();
        
        tableEnv.createFunction("to_timestamp", StrToTimestamp.class);
        tableEnv.createFunction("string_to_long_timestamp", StringTimestampTransform.StringToLong.class);
        tableEnv.createFunction("string_to_sql_timestamp", StringTimestampTransform.StringToSqlTimestamp.class);
        tableEnv.createFunction("long_to_string_timestamp", LongTimestampTransform.LongToString.class);
        tableEnv.createFunction("long_to_sql_timestamp", LongTimestampTransform.LongToSqlTimestamp.class);
        tableEnv.createFunction("sql_to_long_timestamp", SqlTimestampTransform.SqlToLong.class);
        tableEnv.createFunction("simple_string_trans", SimpleStringTransform.class);
        tableEnv.createFunction("get_json_object", GetJsonObject.class);
        tableEnv.createFunction("get_product_center", StoryBaseInfoProductSplit.SplitProductCenter.class);
        tableEnv.createFunction("get_product_name", StoryBaseInfoProductSplit.SplitProductName.class);
        tableEnv.createFunction("string_clean", StringDataCleanTransform.class);
        tableEnv.createFunction("get_first_operator", CurrentOperatorSplit.class);
        tableEnv.registerFunction("string_to_mulcol", new ExpandArrayOneColumnMultRowUDTF());
        tableEnv.registerFunction("is_big_boss", new IsBigBoss(remarkLookupQuery, levelLookUpQuery));
        tableEnv.registerFunction("add_ndays_without_holiday", new AddNdaysWithoutHoliday(holidayLookupQuery));
    }
}