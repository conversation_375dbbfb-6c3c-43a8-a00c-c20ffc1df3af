-- 产品归档信息，需求管理后端的表，需要和需求管理保持一致
create table dim_demand_deploy(
    id              int primary key,
    product_center VARCHAR(255),
    product_name VARCHAR(255),
    product_center_id INT,
    product_name_id INT,
    demand_manager VARCHAR(255),
    interface_person VARCHAR(255),
    update_by VARCHAR(64),
    update_at timestamp(6) without time zone default null,
    workspace_id VARCHAR(255),
    workspace_name VARCHAR(255),
    completed_states VARCHAR(255),
    webhook_id VARCHAR(1024)
);

create index dim_demand_deploy_center_id on dim_demand_deploy(product_center_id);
create index dim_demand_deploy_name_id on dim_demand_deploy(product_name_id);



-- add baymaxxu -09-18
alter table dim_demand_deploy add column  template_id text;
alter table dim_demand_deploy add column  category_id text;
alter table dim_demand_deploy add column  classification_id text;
alter table dim_demand_deploy add column  cloud_type int;
alter table dim_demand_deploy add column  deleted_at text;
alter table dim_demand_deploy add column  status int;
alter table dim_demand_deploy add column  product_module text;
alter table dim_demand_deploy add column  is_product_module_enable int;


-- add cesarezhang -12-25
alter table dim_demand_deploy add column  is_first_approve_necessary int;
alter table dim_demand_deploy add column  product_second_review_roles text;

-- add cesarezhang -01-03
alter table dim_demand_deploy add column  tapd_scheduled_states text;
alter table dim_demand_deploy add column  tapd_rejected_states text;
alter table dim_demand_deploy add column  tapd_bug_completed_states text;

--pg
alter table dim_demand_deploy add column  tapd_estimated_launch_time VARCHAR(255);
--360
alter table dim_demand_deploy add column  tapd_estimated_launch_time VARCHAR(255);
--sr
ALTER TABLE dim_demand_deploy
    ADD COLUMN (tapd_estimated_launch_time string COMMENT 'TAPD预计上线时间配置字段');


