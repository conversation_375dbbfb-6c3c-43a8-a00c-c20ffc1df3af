package com.tencent.andata.smart.lookup;

import com.tencent.andata.utils.lookup.jdbc.AbstractJDBCLookupQuery;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.tencent.andata.utils.udf.DbResultUtils;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import org.apache.calcite.tools.ValidationException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;

public class TicketCallingQuery extends AbstractJDBCLookupQuery<Long, JsonNode> {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final DbResultUtils dbResultUtils = DbResultUtils.getInstance(); // 获取单例实例

    public TicketCallingQuery(DatabaseEnum databaseEnum, DatabaseConf databaseConf) throws ValidationException {
        super(databaseEnum, databaseConf);
    }

    @Override
    protected JsonNode executeQuery(Connection connection, Long ticketId) throws Exception {
        String sql = "SELECT t1.ticket_id,\n"
                + "       t1.user,\n"
                + "       t1.call_id,\n"
                + "       t1.create_time,\n"
                + "       t1.call_direction,\n"
                + "       t2.user_id,\n"
                + "       'cc_operation' AS data_type\n"
                + "FROM dwd_t235_ticket_calling t1\n"
                + "LEFT JOIN dim_customer_staff_info t2 ON t1.user = t2.uid\n"
                + "WHERE ticket_id = ?";

        final PreparedStatement preparedStatement = connection.prepareStatement(sql);
        preparedStatement.setLong(1, ticketId);
        final ResultSet resultSet = preparedStatement.executeQuery();

        // 收集所有结果行
        ArrayNode resultArray = objectMapper.createArrayNode();
        while (resultSet.next()) {
            JsonNode rowNode = dbResultUtils.convertResultSetRowToJsonNode(resultSet);
            resultArray.add(rowNode);
        }

        // 返回结果数组，如果没有记录则返回空数组
        return resultArray;
    }
}