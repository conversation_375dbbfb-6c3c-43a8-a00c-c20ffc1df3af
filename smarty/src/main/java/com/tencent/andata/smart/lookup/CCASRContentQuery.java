package com.tencent.andata.smart.lookup;

import com.tencent.andata.utils.lookup.jdbc.AbstractJDBCLookupQuery;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.tencent.andata.utils.udf.DbResultUtils;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import org.apache.calcite.tools.ValidationException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;

public class CCASRContentQuery extends AbstractJDBCLookupQuery<Long, JsonNode> {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final DbResultUtils dbResultUtils = DbResultUtils.getInstance(); // 获取单例实例

    public CCASRContentQuery(DatabaseEnum databaseEnum, DatabaseConf databaseConf) throws ValidationException {
        super(databaseEnum, databaseConf);
    }

    @Override
    protected JsonNode executeQuery(Connection connection, Long ticketId) throws Exception {

        String sql = "SELECT ticket_id,\n"
                + "       call_id,\n"
                + "       STRING_AGG(content, E'\n' ORDER BY start_time) AS content,\n"
                + "       record_begin AS operate_time\n"
                + "FROM\n"
                + "  (SELECT t1.ticket_id,\n"
                + "          t1.call_id_id AS call_id,\n"
                + "          concat(CASE WHEN t1.role = '客服' then '坐席' ELSE t1.role END, ': ', t1.content) AS content,\n"
                + "          t1.start_time,\n"
                + "          t2.record_begin\n"
                + "   FROM public.public_opinion_cc_record t1\n"
                + "   INNER JOIN cc_qidian_record t2 ON t1.call_id_id = t2.call_id AND t2.task_state = 100\n"
                + "   WHERE t1.ticket_id = ?\n"
                + "  ) AS sub\n"
                + "GROUP BY ticket_id, call_id, operate_time";

        final PreparedStatement preparedStatement = connection.prepareStatement(sql);
        preparedStatement.setLong(1, ticketId);
        final ResultSet resultSet = preparedStatement.executeQuery();

        // 收集所有结果行
        ArrayNode resultArray = objectMapper.createArrayNode();
        while (resultSet.next()) {
            JsonNode rowNode = dbResultUtils.convertResultSetRowToJsonNode(resultSet);
            resultArray.add(rowNode);
        }

        // 返回结果数组，如果没有记录则返回空数组
        return resultArray;
    }
}