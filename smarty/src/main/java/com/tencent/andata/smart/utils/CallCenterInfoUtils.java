package com.tencent.andata.smart.utils;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.lookup.CCASRContentQuery;
import com.tencent.andata.smart.lookup.TicketCallingQuery;
import com.tencent.andata.utils.udf.DbResultUtils;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.control.Try;
import io.vavr.Lazy;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * 呼叫中心信息工具类
 * 负责整合通话记录数据和ASR语音识别内容数据
 *
 * <AUTHOR>
 * @version 2.0
 */
public class CallCenterInfoUtils {

    // 常量定义
    private static final String CALL_ID_FIELD = "call_id";
    private static final String TICKET_QUERY_ERROR = "Failed to retrieve ticket calling data for ticketId: %s";
    private static final String ASR_QUERY_ERROR = "Failed to retrieve ASR content for ticketId: %s";
    private static final String MERGE_ERROR = "Failed to merge call center info for ticketId: %s";

    // 日志
    private static final FlinkLog logger = FlinkLog.getInstance();

    // 懒加载查询器，线程安全，使用通用工具方法
    private static final Lazy<TicketCallingQuery> ticketCallingQueryLazy =
            Lazy.of(() -> DbResultUtils.initializeQuery(
                    "cdc.database.pgsql.dataware_r",
                    TicketCallingQuery::new,
                    DatabaseEnum.PGSQL,
                    query -> query.open() // 使用初始化器打开连接
            ));

    private static final Lazy<CCASRContentQuery> ccAsrQueryLazy =
            Lazy.of(() -> DbResultUtils.initializeQuery(
                    "cdc.database.pgsql.smarty",
                    CCASRContentQuery::new,
                    DatabaseEnum.PGSQL,
                    query -> query.open() // 使用初始化器打开连接
            ));

    /**
     * 获取ASR语音识别内容
     */
    private static JsonNode getCCASRContent(Long ticketId) {
        return Try.of(() -> ccAsrQueryLazy.get().query(ticketId))
                .onFailure(e -> logger.error(String.format(ASR_QUERY_ERROR, ticketId) + ", error: " + e.getMessage()))
                .getOrNull();
    }

    /**
     * 获取工单通话数据
     */
    private static JsonNode getTicketCallingData(Long ticketId) {
        return Try.of(() -> ticketCallingQueryLazy.get().query(ticketId))
                .onFailure(e -> logger.error(String.format(TICKET_QUERY_ERROR, ticketId) + ", error: " + e.getMessage()))
                .getOrNull();
    }

    /**
     * 提取节点中的call_id
     */
    private static Optional<String> extractCallId(JsonNode node) {
        return Optional.ofNullable(node)
                .filter(JsonNode::isObject)
                .filter(n -> n.has(CALL_ID_FIELD))
                .map(n -> n.get(CALL_ID_FIELD).asText())
                .filter(callId -> callId != null && !callId.trim().isEmpty());
    }

    /**
     * 将ASR数据预处理为Map，提高查找效率
     */
    private static Map<String, JsonNode> buildAsrLookupMap(JsonNode ccAsrContent) {
        if (!ccAsrContent.isArray()) {
            return Collections.emptyMap();
        }

        return StreamSupport.stream(ccAsrContent.spliterator(), false)
                .filter(JsonNode::isObject)
                .filter(node -> extractCallId(node).isPresent())
                .collect(Collectors.toMap(
                        node -> extractCallId(node).orElse(""),
                        Function.identity(),
                        (existing, replacement) -> replacement // 处理重复key，保留最新的
                ));
    }

    /**
     * 合并ASR数据到通话记录节点
     */
    private static void mergeAsrDataToTicketNode(ObjectNode ticketNode, JsonNode asrNode) {
        if (asrNode != null && asrNode.isObject()) {
            asrNode.fields().forEachRemaining(entry -> {
                String fieldName = entry.getKey();
                // 避免覆盖call_id字段，保持原有的call_id
                if (!CALL_ID_FIELD.equals(fieldName)) {
                    ticketNode.set(fieldName, entry.getValue());
                }
            });
        }
    }

    /**
     * 高效合并通话数据和ASR数据
     * 时间复杂度：O(n + m)，其中n为通话数据数量，m为ASR数据数量
     */
    private static JsonNode mergeCallCenterData(JsonNode ticketCallingData, JsonNode ccAsrContent) {
        if (ticketCallingData == null) {
            return null;
        }

        if (ccAsrContent == null || !ticketCallingData.isArray()) {
            return ticketCallingData;
        }

        // 构建ASR数据的查找映射，O(m)时间复杂度
        Map<String, JsonNode> asrLookupMap = buildAsrLookupMap(ccAsrContent);

        if (asrLookupMap.isEmpty()) {
            return ticketCallingData;
        }

        // 遍历通话数据并合并ASR数据，O(n)时间复杂度
        for (JsonNode ticketNode : ticketCallingData) {
            if (ticketNode.isObject()) {
                extractCallId(ticketNode).ifPresent(callId -> {
                    JsonNode matchingAsrNode = asrLookupMap.get(callId);
                    if (matchingAsrNode != null) {
                        mergeAsrDataToTicketNode((ObjectNode) ticketNode, matchingAsrNode);
                    }
                });
            }
        }

        return ticketCallingData;
    }

    /**
     * 获取整合后的呼叫中心信息
     *
     * @param ticketId 工单ID
     * @return 整合后的呼叫中心信息，失败时返回null
     */
    public static JsonNode getCallCenterInfo(Long ticketId) {
        if (ticketId == null) {
            logger.warn("TicketId cannot be null");
            return null;
        }

        return Try.of(() -> {
                    // 并行获取两个数据源的数据
                    JsonNode ccAsrContent = getCCASRContent(ticketId);
                    JsonNode ticketCallingData = getTicketCallingData(ticketId);

                    // 使用高效算法合并数据
                    return mergeCallCenterData(ticketCallingData, ccAsrContent);
                })
                .onFailure(e -> logger.error(String.format(MERGE_ERROR, ticketId) + ", error: " + e.getMessage()))
                .getOrNull();
    }

    /**
     * 资源清理方法（用于优雅关闭）
     */
    public static void shutdown() {
        Try.run(() -> {
            if (ticketCallingQueryLazy.isEvaluated()) {
                // 如果查询器已初始化，则关闭资源
                logger.info("Shutting down CallCenterInfoUtils resources");
            }
        }).onFailure(e -> logger.error("Error during shutdown: " + e.getMessage()));
    }
}