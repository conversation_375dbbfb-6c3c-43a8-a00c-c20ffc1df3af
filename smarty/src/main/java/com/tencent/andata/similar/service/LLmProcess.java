package com.tencent.andata.similar.service;

import static com.tencent.andata.similar.util.BusinessUtils.generateSimilarQuestionsPrompt;
import static com.tencent.andata.similar.util.JsonMergeUtils.addStringField;
import static com.tencent.andata.similar.util.JsonMergeUtils.getTextValue;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.utils.HttpClientUtils;
import com.tencent.andata.utils.gpt.CommonGptApiV2;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LLmProcess extends ProcessFunction<JsonNode, JsonNode> {

    private transient FlinkLog zhiyanLogger = FlinkLog.getInstance();
    private Logger log = LoggerFactory.getLogger(LLmProcess.class);

    private transient HttpClientUtils gptClient;

    private Boolean isGenerate;
    private transient ObjectMapper mapper;
    private String similarTicketPrompt;
    private String modelUrl;
    private String modelToken;
    private int similarGenerateNum;

    public LLmProcess(Boolean isGenerate, ObjectMapper mapper, String similarTicketPrompt, int similarGenerateNum,
            String modelUrl, String modelToken) {
        this.isGenerate = isGenerate;
        this.mapper = mapper;
        this.similarTicketPrompt = similarTicketPrompt;
        this.similarGenerateNum = similarGenerateNum;
        this.modelUrl = modelUrl;
        this.modelToken = modelToken;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        // 重新初始化 transient 字段
        zhiyanLogger = FlinkLog.getInstance();
        
        // 重新初始化 mapper，避免 NullPointerException
        if (mapper == null) {
            mapper = new ObjectMapper();
        }

        gptClient = HttpClientUtils.builder()
                .connectTimeout(10000)           // 10秒连接超时
                .socketTimeout(240000)           // 240秒读取超时（4分钟）
                .maxRetries(6)                   // 重试5次
                .retryIntervalMs(10000)   // 重试间隔10秒
                .build();
    }

    @Override
    public void processElement(JsonNode jsonNode, Context context, Collector<JsonNode> collector)
            throws Exception {

        String ticketId = "";
        String requestId = "";
        if (!isGenerate) {
            JsonNode res = addStringField(jsonNode, "LLM_Res", "{\"answer\":\"[]\"}", mapper);
            JsonNode success = addStringField(res, "gpt_processing_status", "success", mapper);
            collector.collect(success);
        }else{
            try {
                ticketId = getTextValue(jsonNode, "ticket_id", "");
                requestId = getTextValue(jsonNode, "request-id", "");
                String originalQuestion = getTextValue(jsonNode, "question_description", "");

                String prompt = generateSimilarQuestionsPrompt(similarTicketPrompt, originalQuestion,
                        similarGenerateNum);

                String gptResponse = callGptApiWithParams(prompt, requestId, gptClient, modelUrl, modelToken);
                if (gptResponse != null && !gptResponse.trim().isEmpty()) {
                    JsonNode res = addStringField(jsonNode, "LLM_Res", gptResponse, mapper);
                    JsonNode success = addStringField(res, "gpt_processing_status", "success", mapper);
                    collector.collect(success);
                } else {
                    JsonNode res = addStringField(jsonNode, "LLM_Res", "{\"answer\":\"[]\"}", mapper);
                    JsonNode degraded = addStringField(res, "gpt_processing_status", "degraded", mapper);
                    JsonNode degradedWithReason = addStringField(degraded, "gpt_error_reason",
                            "Empty response from GPT API", mapper);
                    zhiyanLogger.warn("GPT响应为空: ticket_id={}", ticketId);
                    collector.collect(degradedWithReason);
                }

            } catch (Exception e) {
                zhiyanLogger.error("GPT处理失败: error=" + ticketId + ":" + e.getMessage());
                JsonNode res = addStringField(jsonNode, "LLM_Res", "{\"answer\":\"[]\"}", mapper);
                JsonNode failed = addStringField(res, "gpt_processing_status", "failed", mapper);
                JsonNode failedWithError = addStringField(failed, "gpt_error_reason", e.getMessage(), mapper);
                collector.collect(failedWithError);
            }
        }
    }

    @Override
    public void close() throws Exception {
        gptClient.close();
        super.close();

    }


    private static String callGptApiWithParams(String prompt, String requestId, HttpClientUtils client,
            String modelUrl, String modelToken) {
        try {
            CommonGptApiV2 gptApi = new CommonGptApiV2(modelToken, modelUrl, "cloud1_deepseek-v3");

            // 设置额外参数
            ConcurrentHashMap<String, String> extraParams = new ConcurrentHashMap<>();
            extraParams.put("api_type", "azure");
            gptApi.setExtraParams(extraParams);

            // 构建消息
            CopyOnWriteArrayList<ConcurrentHashMap<String, Object>> messages = new CopyOnWriteArrayList<>();
            ConcurrentHashMap<String, Object> userMessage = new ConcurrentHashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", prompt);
            messages.add(userMessage);

            gptApi.setMessages(messages);
            gptApi.buildRequestWithRid(prompt, requestId);

            // 5. 执行请求
            return gptApi.requestAndParseSync(client);
        } catch (Exception e) {
            return "{\"answer\":\"[]\"}";
        }
    }
}