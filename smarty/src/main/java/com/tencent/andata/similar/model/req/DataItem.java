package com.tencent.andata.similar.model.req;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataItem {
    private String ID; // source_name-source_type-source_id
    private String doc; // question
    private Integer deleted = 0; // 0:有效 1:软删除
    private Integer create_time; // 秒级时间戳
    private ItemFields fields;
    private transient Integer chunk; // 不序列化
    private transient List<Float> vector; // 不序列化
    private transient Integer dataset; // 不序列化


    // 构建方法示例
    public static DataItem buildItem(String sourceType, String sourceId, String question, 
                                    String solution, ItemFields fields) {
        DataItem item = new DataItem();
        item.setID(String.format("similar_search-%s-%s", sourceType, sourceId));
        item.setDoc(question);
        item.setFields(fields);
        return item;
    }
}