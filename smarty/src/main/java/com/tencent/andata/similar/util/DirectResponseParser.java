package com.tencent.andata.similar.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.core.JsonParser;
import com.tencent.andata.similar.model.DirectLLMResponse;
import java.util.List;
import java.util.ArrayList;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

public class DirectResponseParser {
    private static final ObjectMapper objectMapper = new ObjectMapper()
            .configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true)
            .configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
    private static final Pattern JSON_PATTERN = Pattern.compile("```json\\n(.*?)\\n```", Pattern.DOTALL);

    // 回调接口用于重新调用AI
    public interface AIRetryCallback {
        String retryAICall() throws Exception;
    }

    public static DirectLLMResponse parseResponse(String jsonString) throws Exception {
        return parseResponseWithRetry(jsonString, 3); // 默认重试3次
    }

    public static DirectLLMResponse parseResponseWithRetry(String jsonString, int maxRetries) throws Exception {
        DirectLLMResponse response = objectMapper.readValue(jsonString, DirectLLMResponse.class);

        String answer = response.getAnswer();
        Matcher matcher = JSON_PATTERN.matcher(answer);

        if (matcher.find()) {
            String jsonArrayStr = matcher.group(1);

            for (int attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    // 尝试清理和修复JSON字符串
                    String cleanedJsonStr = cleanJsonString(jsonArrayStr);
                    List<String> answers = objectMapper.readValue(cleanedJsonStr,
                            new TypeReference<List<String>>() {});

                    // 验证解析结果是否有效
                    if (answers != null && !answers.isEmpty() && isValidAnswers(answers)) {
                        response.setParsedAnswers(answers);
                        return response;
                    }

                    // 如果解析结果无效，记录并准备重试
                    System.out.println("Attempt " + attempt + ": Parsed answers are invalid or empty");

                } catch (Exception e) {
                    System.err.println("Attempt " + attempt + " failed to parse JSON: " + e.getMessage());

                    if (attempt == maxRetries) {
                        // 最后一次尝试失败，设置空列表并抛出异常
                        response.setParsedAnswers(new ArrayList<>());
                        throw new RuntimeException("Failed to parse JSON after " + maxRetries + " attempts. Last error: " + e.getMessage(), e);
                    }
                }

                // 如果不是最后一次尝试，等待一段时间后重试
                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(1000); // 等待1秒后重试
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Retry interrupted", ie);
                    }

                    System.out.println("Preparing to retry parsing...");
                }
            }
        }

        // 如果没有找到JSON数组模式，设置空列表
        response.setParsedAnswers(new ArrayList<>());
        return response;
    }

    public static DirectLLMResponse parseResponseWithAIRetry(String jsonString,
            AIRetryCallback retryCallback,
            int maxRetries) throws Exception {
        String currentJsonString = jsonString;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                DirectLLMResponse response = parseResponseWithRetry(currentJsonString, 1);

                // 如果解析成功且结果有效，直接返回
                if (response.getParsedAnswers() != null && !response.getParsedAnswers().isEmpty()) {
                    return response;
                }

            } catch (Exception e) {
                System.err.println("Attempt " + attempt + " failed: " + e.getMessage());
            }

            // 如果不是最后一次尝试，重新调用AI
            if (attempt < maxRetries && retryCallback != null) {
                try {
                    System.out.println("Retrying AI call, attempt " + (attempt + 1));
                    currentJsonString = retryCallback.retryAICall();
                } catch (Exception e) {
                    System.err.println("AI retry call failed: " + e.getMessage());
                }
            }
        }

        throw new RuntimeException("Failed to get valid AI response after " + maxRetries + " attempts");
    }

    // 增强的JSON字符串清理方法
    private static String cleanJsonString(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return "[]";
        }

        String cleaned = jsonStr.trim();

        // 如果不是数组格式，直接返回基本清理结果
        if (!cleaned.startsWith("[") || !cleaned.endsWith("]")) {
            return basicCleanJsonString(cleaned);
        }

        try {
            // 尝试智能重构JSON数组
            return rebuildJsonArray(cleaned);
        } catch (Exception e) {
            System.err.println("Smart JSON rebuild failed: " + e.getMessage() + ", falling back to basic cleaning");
            // 降级到基本清理
            return basicCleanJsonString(cleaned);
        }
    }

    // 基本的JSON字符串清理
    private static String basicCleanJsonString(String jsonStr) {
        return jsonStr
                .replace("\\'", "'")     // 移除单引号转义
                .replace("\\\"", "\"")   // 处理双引号转义
                .replace("'", "\"")      // 将单引号替换为双引号
                .trim();
    }

    // 智能重构JSON数组
    private static String rebuildJsonArray(String arrayStr) {
        // 移除首尾的方括号
        String content = arrayStr.substring(1, arrayStr.length() - 1).trim();
        if (content.isEmpty()) {
            return "[]";
        }

        List<String> items = parseArrayItems(content);

        // 重新构建JSON数组
        StringBuilder result = new StringBuilder("[");
        for (int i = 0; i < items.size(); i++) {
            if (i > 0) {
                result.append(",");
            }
            result.append("\"").append(escapeJsonString(items.get(i))).append("\"");
        }
        result.append("]");

        return result.toString();
    }

    // 解析数组项，处理复杂的引号和转义情况
    private static List<String> parseArrayItems(String content) {
        List<String> items = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        boolean inString = false;
        char stringDelimiter = '"';
        int bracketLevel = 0;

        for (int i = 0; i < content.length(); i++) {
            char c = content.charAt(i);
            char next = (i + 1 < content.length()) ? content.charAt(i + 1) : '\0';
            char prev = (i > 0) ? content.charAt(i - 1) : '\0';

            if (!inString) {
                if (c == '"' || c == '\'') {
                    // 开始一个字符串
                    inString = true;
                    stringDelimiter = c;
                    // 不添加起始引号
                } else if (c == ',' && bracketLevel == 0) {
                    // 在顶层找到分隔符
                    String item = current.toString().trim();
                    if (!item.isEmpty()) {
                        items.add(item);
                    }
                    current.setLength(0);
                } else if (c == '[') {
                    bracketLevel++;
                    current.append(c);
                } else if (c == ']') {
                    bracketLevel--;
                    current.append(c);
                } else if (!Character.isWhitespace(c) || current.length() > 0) {
                    current.append(c);
                }
            } else {
                // 在字符串内部
                if (c == '\\') {
                    // 处理转义字符
                    if (next == '\\') {
                        current.append('\\');
                        i++; // 跳过下一个字符
                    } else if (next == '"') {
                        current.append('"');
                        i++; // 跳过下一个字符
                    } else if (next == '\'') {
                        current.append('\'');
                        i++; // 跳过下一个字符
                    } else if (next == 'n') {
                        current.append('\n');
                        i++; // 跳过下一个字符
                    } else if (next == 't') {
                        current.append('\t');
                        i++; // 跳过下一个字符
                    } else if (next == 'r') {
                        current.append('\r');
                        i++; // 跳过下一个字符
                    } else {
                        // 其他情况，保持原字符（不是转义）
                        current.append(c);
                    }
                } else if (c == stringDelimiter) {
                    // 可能的字符串结束
                    if (isStringEndDelimiter(content, i, stringDelimiter)) {
                        inString = false;
                        // 不添加结束引号
                    } else {
                        // 字符串内部的同类引号
                        current.append(c);
                    }
                } else {
                    current.append(c);
                }
            }
        }

        // 添加最后一项
        String item = current.toString().trim();
        if (!item.isEmpty()) {
            items.add(item);
        }

        return items;
    }

    // 判断是否是字符串结束分隔符
    private static boolean isStringEndDelimiter(String content, int pos, char delimiter) {
        // 简单启发式：查看后面的字符
        for (int i = pos + 1; i < content.length(); i++) {
            char c = content.charAt(i);
            if (Character.isWhitespace(c)) {
                continue; // 跳过空白字符
            }
            // 如果下一个非空白字符是逗号或到达末尾，则这是结束引号
            return c == ',' || c == ']';
        }
        return true; // 到达字符串末尾
    }

    // 转义JSON字符串中的特殊字符
    private static String escapeJsonString(String str) {
        if (str == null) {
            return "";
        }

        return str.replace("\\", "\\\\")     // 反斜杠（必须最先处理）
                .replace("\"", "\\\"")      // 双引号
                .replace("\b", "\\b")       // 退格
                .replace("\f", "\\f")       // 换页
                .replace("\n", "\\n")       // 换行
                .replace("\r", "\\r")       // 回车
                .replace("\t", "\\t");      // 制表符
    }

    // 验证解析结果是否有效
    private static boolean isValidAnswers(List<String> answers) {
        if (answers == null || answers.isEmpty()) {
            return false;
        }

        // 检查是否包含有意义的内容
        for (String answer : answers) {
            if (answer != null && answer.trim().length() > 0 && !answer.trim().equals("null")) {
                return true;
            }
        }

        return false;
    }
}