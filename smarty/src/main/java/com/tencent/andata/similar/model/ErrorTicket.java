package com.tencent.andata.similar.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 错误票据模型类
 * 用于记录特征提取失败的票据信息
 */
public class ErrorTicket implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 票据ID
     */
    private String ticketId;
    
    /**
     * 错误消息
     */
    private String msg;
    
    /**
     * 错误状态码
     */
    private Integer status;
    
    /**
     * 创建时间（用于排查问题）
     */
    private LocalDateTime createTime;
    
    /**
     * 默认构造函数
     */
    public ErrorTicket() {
        this.createTime = LocalDateTime.now();
    }
    
    /**
     * 带参数的构造函数
     * 
     * @param ticketId 票据ID
     * @param msg 错误消息
     * @param status 错误状态码
     */
    public ErrorTicket(String ticketId, String msg, Integer status) {
        this.ticketId = ticketId;
        this.msg = msg;
        this.status = status;
        this.createTime = LocalDateTime.now();
    }
    
    // Getter 和 Setter 方法
    public String getTicketId() {
        return ticketId;
    }
    
    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }
    
    public String getMsg() {
        return msg;
    }
    
    public void setMsg(String msg) {
        this.msg = msg;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    /**
     * 获取格式化的创建时间
     * 
     * @return 格式化的时间字符串 (yyyy-MM-dd HH:mm:ss)
     */
    public String getFormattedCreateTime() {
        if (createTime != null) {
            return createTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }
    
    /**
     * 验证必填字段
     * 
     * @return 如果所有必填字段都有值则返回 true
     */
    public boolean isValid() {
        return ticketId != null && !ticketId.trim().isEmpty() 
               && msg != null && !msg.trim().isEmpty() 
               && status != null;
    }
    
    @Override
    public String toString() {
        return "ErrorTicket{" +
                "ticketId='" + ticketId + '\'' +
                ", msg='" + msg + '\'' +
                ", status=" + status +
                ", createTime=" + getFormattedCreateTime() +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        ErrorTicket that = (ErrorTicket) o;
        
        if (!ticketId.equals(that.ticketId)) return false;
        if (!msg.equals(that.msg)) return false;
        return status.equals(that.status);
    }
    
    @Override
    public int hashCode() {
        int result = ticketId.hashCode();
        result = 31 * result + msg.hashCode();
        result = 31 * result + status.hashCode();
        return result;
    }
}