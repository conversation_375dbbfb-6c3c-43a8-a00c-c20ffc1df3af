package com.tencent.andata.similar.util;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.similar.model.ErrorTicket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 错误票据异步写入 PostgreSQL 工具类
 * 🔧 避免使用 Lambda 表达式，解决序列化问题
 */
public class ErrorTicketSink {

    private static final Logger log = LoggerFactory.getLogger(ErrorTicketSink.class);
    private final FlinkLog zhiyanLogger = FlinkLog.getInstance();

    // 数据库连接配置
    private final String jdbcUrl;
    private final String username;
    private final String password;
    private final int batchSize;

    // 线程池和缓冲区
    private transient ExecutorService asyncExecutor;
    private transient BlockingQueue<ErrorTicket> buffer;
    private transient ScheduledExecutorService flushExecutor;

    // 统计信息
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong totalFailed = new AtomicLong(0);
    private final AtomicBoolean isShuttingDown = new AtomicBoolean(false);

    // SQL 语句
    private static final String INSERT_SQL =
            "INSERT INTO similar_extract_fail (ticket_id, msg, status, create_time) VALUES (?, ?, ?, ?)";

    /**
     * 构造函数
     */
    public ErrorTicketSink(String jdbcUrl, String username, String password, int batchSize) {
        this.jdbcUrl = jdbcUrl;
        this.username = username;
        this.password = password;
        this.batchSize = batchSize > 0 ? batchSize : 3;

        // 🔧 延迟初始化线程池，避免序列化问题
        initializeComponents();
    }

    /**
     * 🔧 初始化组件 - 避免在构造函数中使用 Lambda
     */
    private void initializeComponents() {
        // 初始化线程池 - 使用匿名类而不是 Lambda
        this.asyncExecutor = Executors.newFixedThreadPool(2, new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r, "ErrorTicketSink-Worker");
                t.setDaemon(false);
                return t;
            }
        });

        this.buffer = new ArrayBlockingQueue<>(batchSize * 20);

        this.flushExecutor = Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r, "ErrorTicketSink-Flush");
                t.setDaemon(false);
                return t;
            }
        });

        // 🔧 使用匿名类而不是方法引用
        flushExecutor.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                flushBuffer();
            }
        }, 20, 15, TimeUnit.SECONDS);

        // 测试数据库连接
        testDatabaseConnection();

        log.info("[ErrorTicketSink] 初始化完成，批次大小: {}, 缓冲区大小: {}",
                batchSize, buffer.remainingCapacity() + batchSize);
    }

    /**
     * 🔧 测试数据库连接并插入Mock数据验证功能
     */
    private void testDatabaseConnection() {
        try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password)) {
            log.info("[ErrorTicketSink] 数据库连接测试成功");

            // 1. 测试表是否存在
            try (PreparedStatement pstmt = connection.prepareStatement(
                    "SELECT COUNT(*) FROM similar_extract_fail WHERE 1=0")) {
                pstmt.executeQuery();
                log.info("[ErrorTicketSink] 目标表 similar_extract_fail 验证成功");
            } catch (SQLException tableException) {
                log.error("[ErrorTicketSink] 目标表 similar_extract_fail 不存在或结构异常: {}",
                        tableException.getMessage());
                zhiyanLogger.error("[ErrorTicketSink] 数据库表验证失败: " + tableException.getMessage());
                return; // 表不存在就不继续测试了
            }

            // 2. 🔧 新增：插入Mock数据测试写入功能
            try {
                // 创建测试用的Mock数据
                ErrorTicket mockErrorTicket = new ErrorTicket(
                        "TEST_TICKET_" + System.currentTimeMillis(), // 使用时间戳确保唯一性
                        "ErrorTicketSink初始化测试 - Mock数据插入验证",
                        666  // 🔧 使用666作为测试状态码
                );

                // 测试插入
                try (PreparedStatement insertStmt = connection.prepareStatement(INSERT_SQL)) {
                    insertStmt.setString(1, mockErrorTicket.getTicketId());
                    insertStmt.setString(2, mockErrorTicket.getMsg());
                    insertStmt.setInt(3, mockErrorTicket.getStatus());
                    insertStmt.setTimestamp(4, Timestamp.valueOf(mockErrorTicket.getCreateTime()));

                    int result = insertStmt.executeUpdate();

                    if (result > 0) {
                        // 🔧 成功插入，输出666表示测试通过
                        log.info("[ErrorTicketSink] Mock数据插入成功 ✅ - 测试代码: 666, ticket_id: {}",
                                mockErrorTicket.getTicketId());
                        zhiyanLogger.info(
                                String.format("[ErrorTicketSink] 数据库写入功能验证成功 - 返回码: 666, Mock票据: %s",
                                        mockErrorTicket.getTicketId()));

                        // 3. 验证刚插入的数据能否正确读取
                        try (PreparedStatement selectStmt = connection.prepareStatement(
                                "SELECT ticket_id, msg, status FROM similar_extract_fail WHERE ticket_id = ? AND status = 666")) {
                            selectStmt.setString(1, mockErrorTicket.getTicketId());

                            try (ResultSet rs = selectStmt.executeQuery()) {
                                if (rs.next()) {
                                    String retrievedTicketId = rs.getString("ticket_id");
                                    String retrievedMsg = rs.getString("msg");
                                    int retrievedStatus = rs.getInt("status");

                                    log.info(
                                            "[ErrorTicketSink] Mock数据读取验证成功 ✅ - ticket_id: {}, status: {}, msg: {}",
                                            retrievedTicketId, retrievedStatus, retrievedMsg);
                                    zhiyanLogger.info(
                                            String.format("[ErrorTicketSink] 完整功能验证通过 - 测试码: 666 ✅"));
                                } else {
                                    log.warn("[ErrorTicketSink] Mock数据读取失败 - 插入的数据无法查询到");
                                }
                            }
                        }

                        // 4. 🔧 可选：清理测试数据（如果不想保留的话）
                        try (PreparedStatement deleteStmt = connection.prepareStatement(
                                "DELETE FROM similar_extract_fail WHERE ticket_id = ? AND status = 666")) {
                            deleteStmt.setString(1, mockErrorTicket.getTicketId());
                            int deleteResult = deleteStmt.executeUpdate();

                            if (deleteResult > 0) {
                                log.info("[ErrorTicketSink] Mock测试数据清理完成 🧹 - ticket_id: {}",
                                        mockErrorTicket.getTicketId());
                            }
                        } catch (SQLException deleteException) {
                            log.warn("[ErrorTicketSink] Mock测试数据清理失败（非关键错误）: {}",
                                    deleteException.getMessage());
                        }

                    } else {
                        log.error("[ErrorTicketSink] Mock数据插入失败 ❌ - 插入结果: {}", result);
                        zhiyanLogger.error("[ErrorTicketSink] 数据库写入功能验证失败");
                    }
                }

            } catch (SQLException insertException) {
                log.error("[ErrorTicketSink] Mock数据插入测试异常 ❌ - error: {}",
                        insertException.getMessage(), insertException);
                zhiyanLogger.error("[ErrorTicketSink] 数据库写入测试失败: " + insertException.getMessage());
            }

        } catch (SQLException e) {
            log.error("[ErrorTicketSink] 数据库连接测试失败: {}", e.getMessage(), e);
            zhiyanLogger.error("[ErrorTicketSink] 数据库连接失败: " + e.getMessage());
        }
    }

    /**
     * 异步写入错误票据
     */
    public void writeAsync(ErrorTicket errorTicket) {
        if (isShuttingDown.get()) {
            log.warn("[ErrorTicketSink] 系统正在关闭，拒绝新的写入请求: {}",
                    errorTicket != null ? errorTicket.getTicketId() : "null");
            return;
        }

        if (errorTicket == null) {
            log.warn("[ErrorTicketSink] 错误票据为 null，跳过写入");
            totalFailed.incrementAndGet();
            return;
        }

        if (!errorTicket.isValid()) {
            log.warn("[ErrorTicketSink] 无效的错误票据，跳过写入: ticket_id={}, msg={}, status={}",
                    errorTicket.getTicketId(), errorTicket.getMsg(), errorTicket.getStatus());
            totalFailed.incrementAndGet();
            return;
        }

        log.debug("[ErrorTicketSink] 开始异步写入错误票据: {}", errorTicket);

        // 🔧 使用匿名类而不是 Lambda
        asyncExecutor.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    // 尝试添加到缓冲区
                    if (!buffer.offer(errorTicket, 3, TimeUnit.SECONDS)) {
                        log.warn("[ErrorTicketSink] 缓冲区已满，强制刷新: ticket_id={}", errorTicket.getTicketId());
                        flushBuffer();

                        // 再次尝试添加
                        if (!buffer.offer(errorTicket, 3, TimeUnit.SECONDS)) {
                            log.error("[ErrorTicketSink] 写入失败，缓冲区仍然满: ticket_id={}",
                                    errorTicket.getTicketId());
                            totalFailed.incrementAndGet();
                            return;
                        }
                    }

                    // 如果缓冲区达到批次大小，立即刷新
                    if (buffer.size() >= batchSize) {
                        flushBuffer();
                    }

                    log.debug("[ErrorTicketSink] 错误票据已添加到缓冲区: ticket_id={}, 当前缓冲区大小: {}",
                            errorTicket.getTicketId(), buffer.size());

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("[ErrorTicketSink] 写入被中断: ticket_id={}", errorTicket.getTicketId());
                    totalFailed.incrementAndGet();
                } catch (Exception e) {
                    log.error("[ErrorTicketSink] 写入异常: ticket_id={}, error={}",
                            errorTicket.getTicketId(), e.getMessage(), e);
                    totalFailed.incrementAndGet();
                }
            }
        });
    }

    /**
     * 刷新缓冲区，批量写入数据库
     */
    private synchronized void flushBuffer() {
        if (buffer == null || buffer.isEmpty()) {
            return;
        }

        List<ErrorTicket> batch = new ArrayList<>();
        buffer.drainTo(batch, batchSize);

        if (batch.isEmpty()) {
            return;
        }

        long startTime = System.currentTimeMillis();

        try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password);
                PreparedStatement pstmt = connection.prepareStatement(INSERT_SQL)) {

            connection.setAutoCommit(false);

            for (ErrorTicket errorTicket : batch) {
                try {
                    pstmt.setString(1, errorTicket.getTicketId());
                    pstmt.setString(2, errorTicket.getMsg());
                    pstmt.setInt(3, errorTicket.getStatus());
                    pstmt.setTimestamp(4, Timestamp.valueOf(errorTicket.getCreateTime()));
                    pstmt.addBatch();
                } catch (Exception e) {
                    log.error("[ErrorTicketSink] 准备语句失败: ticket_id={}, error={}",
                            errorTicket.getTicketId(), e.getMessage());
                }
            }

            int[] results = pstmt.executeBatch();
            connection.commit();

            // 统计成功和失败的数量
            int successCount = 0;
            int failCount = 0;
            for (int result : results) {
                if (result > 0) {
                    successCount++;
                } else {
                    failCount++;
                }
            }

            totalProcessed.addAndGet(successCount);
            totalFailed.addAndGet(failCount);

            long endTime = System.currentTimeMillis();

            log.info("[ErrorTicketSink] 批量写入完成: 成功={}, 失败={}, 耗时={}ms",
                    successCount, failCount, (endTime - startTime));

            zhiyanLogger.info(String.format("[ErrorTicketSink] 批量写入完成: 成功=%d, 失败=%d, 耗时=%dms",
                    successCount, failCount, (endTime - startTime)));

        } catch (SQLException e) {
            log.error("[ErrorTicketSink] 数据库操作失败: batch_size={}, error={}", batch.size(), e.getMessage(), e);
            totalFailed.addAndGet(batch.size());

            // 将失败的数据重新放回缓冲区（如果空间允许且系统未关闭）
            if (!isShuttingDown.get() && buffer != null) {
                for (ErrorTicket errorTicket : batch) {
                    if (!buffer.offer(errorTicket)) {
                        log.warn("[ErrorTicketSink] 重新入队失败: ticket_id={}", errorTicket.getTicketId());
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("[ErrorTicketSink] 意外错误: batch_size={}, error={}", batch.size(), e.getMessage(), e);
            totalFailed.addAndGet(batch.size());
        }
    }

    /**
     * 🔧 补充：获取统计信息
     */
    public String getStatistics() {
        return String.format("ErrorTicketSink统计: 已处理=%d, 失败=%d, 缓冲区大小=%d",
                totalProcessed.get(), totalFailed.get(),
                buffer != null ? buffer.size() : 0);
    }

    /**
     * 🔧 补充：强制刷新缓冲区的公共方法
     */
    public void forceFlush() {
        log.info("[ErrorTicketSink] 强制刷新缓冲区，当前缓冲区大小: {}",
                buffer != null ? buffer.size() : 0);
        flushBuffer();
    }

    /**
     * 🔧 补充：检查是否健康
     */
    public boolean isHealthy() {
        return !isShuttingDown.get() &&
                asyncExecutor != null && !asyncExecutor.isShutdown() &&
                flushExecutor != null && !flushExecutor.isShutdown();
    }

    /**
     * 🔧 补充：同步写入方法 - 用于关键错误的立即写入，不经过缓冲区
     */
    public boolean writeSyncImmediate(ErrorTicket errorTicket) {
        if (errorTicket == null || !errorTicket.isValid()) {
            log.warn("[ErrorTicketSink] 无效的错误票据，跳过同步写入: {}", errorTicket);
            return false;
        }

        try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password);
                PreparedStatement pstmt = connection.prepareStatement(INSERT_SQL)) {

            pstmt.setString(1, errorTicket.getTicketId());
            pstmt.setString(2, errorTicket.getMsg());
            pstmt.setInt(3, errorTicket.getStatus());
            pstmt.setTimestamp(4, Timestamp.valueOf(errorTicket.getCreateTime()));

            int result = pstmt.executeUpdate();

            if (result > 0) {
                totalProcessed.incrementAndGet();
                log.info("[ErrorTicketSink] 同步写入成功: ticket_id={}", errorTicket.getTicketId());
                return true;
            } else {
                totalFailed.incrementAndGet();
                log.warn("[ErrorTicketSink] 同步写入未影响任何行: ticket_id={}", errorTicket.getTicketId());
                return false;
            }

        } catch (SQLException e) {
            totalFailed.incrementAndGet();
            log.error("[ErrorTicketSink] 同步写入失败: ticket_id={}, error={}",
                    errorTicket.getTicketId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 🔧 补充：优雅关闭 - 确保所有数据都写入
     */
    public void shutdown() {
        log.info("[ErrorTicketSink] 开始关闭，当前缓冲区大小: {}",
                buffer != null ? buffer.size() : 0);

        // 标记系统正在关闭
        isShuttingDown.set(true);

        try {
            // 1. 先停止接收新的写入请求（通过isShuttingDown标志）
            log.info("[ErrorTicketSink] 已标记为关闭状态，停止接收新请求");

            // 2. 停止定时刷新任务
            if (flushExecutor != null) {
                flushExecutor.shutdown();
            }

            // 3. 多次刷新确保数据写入完成
            int maxFlushAttempts = 10;
            for (int i = 0; i < maxFlushAttempts && buffer != null && !buffer.isEmpty(); i++) {
                log.info("[ErrorTicketSink] 第{}次最终刷新，剩余数据: {}", i + 1, buffer.size());
                flushBuffer();

                // 等待一小段时间确保写入完成
                if (!buffer.isEmpty()) {
                    Thread.sleep(500);
                }
            }

            // 4. 如果仍有数据，强制同步写入
            if (buffer != null && !buffer.isEmpty()) {
                log.warn("[ErrorTicketSink] 缓冲区仍有{}条数据，执行强制同步写入", buffer.size());
                forceWriteRemainingData();
            }

            // 5. 关闭异步执行器
            if (asyncExecutor != null) {
                asyncExecutor.shutdown();
                if (!asyncExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.warn("[ErrorTicketSink] 异步执行器未在60秒内关闭，强制关闭");
                    asyncExecutor.shutdownNow();
                }
            }

            // 6. 等待定时执行器关闭
            if (flushExecutor != null && !flushExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                log.warn("[ErrorTicketSink] 定时执行器未在10秒内关闭，强制关闭");
                flushExecutor.shutdownNow();
            }

            log.info("[ErrorTicketSink] 关闭完成，最终统计: {}", getStatistics());

        } catch (Exception e) {
            log.error("[ErrorTicketSink] 关闭过程中发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 🔧 补充：强制写入剩余数据
     */
    private void forceWriteRemainingData() {
        if (buffer == null) {
            return;
        }

        List<ErrorTicket> remainingData = new ArrayList<>();
        buffer.drainTo(remainingData);

        if (remainingData.isEmpty()) {
            return;
        }

        log.info("[ErrorTicketSink] 强制同步写入剩余{}条数据", remainingData.size());

        for (ErrorTicket errorTicket : remainingData) {
            try {
                boolean success = writeSyncImmediate(errorTicket);
                if (!success) {
                    log.error("[ErrorTicketSink] 强制写入失败: ticket_id={}", errorTicket.getTicketId());
                }
            } catch (Exception e) {
                log.error("[ErrorTicketSink] 强制写入异常: ticket_id={}, error={}",
                        errorTicket.getTicketId(), e.getMessage());
            }
        }
    }

    /**
     * 🔧 补充：获取详细统计信息
     */
    public String getDetailedStatistics() {
        return String.format(
                "ErrorTicketSink详细统计: 已处理=%d, 失败=%d, 缓冲区大小=%d, 系统状态=%s, 健康状态=%s",
                totalProcessed.get(),
                totalFailed.get(),
                buffer != null ? buffer.size() : 0,
                isShuttingDown.get() ? "关闭中" : "运行中",
                isHealthy() ? "健康" : "异常"
        );
    }

    /**
     * 🔧 补充：获取缓冲区状态
     */
    public String getBufferStatus() {
        if (buffer == null) {
            return "缓冲区状态: 未初始化";
        }
        return String.format("缓冲区状态: 当前大小=%d, 剩余容量=%d, 总容量=%d",
                buffer.size(), buffer.remainingCapacity(),
                buffer.size() + buffer.remainingCapacity());
    }
}