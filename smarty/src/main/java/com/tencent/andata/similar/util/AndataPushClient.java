package com.tencent.andata.similar.util;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.*;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.*;
import com.tencent.andata.utils.HttpClientUtils;
import java.util.HashMap;

/**
 * 机器人推vx文档：https://developer.work.weixin.qq.com/document/path/99398
 * */
public class AndataPushClient {
    private final String chatBotWebHookUrl = "https://in.qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3c21dec6-99bd-4202-b166-994e303e0a60";
    private final ObjectMapper objectMapper;

    /**
     * 消息类型枚举
     */
    enum MessageType {
        TEXT {
            @Override
            public String toString() {
                return "text";
            }
        }, IMAGE {
            @Override
            public String toString() {
                return "image";
            }
        }, MARKDOWN {
            @Override
            public String toString() {
                return "markdown";
            }
        }
    }

    public AndataPushClient() {
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 机器人发送文本消息
     *
     * @param chatId  群ID
     * @param message 消息内容
     * @throws Exception
     */
    public void pushMessage(String chatId, String message) throws Exception {
        ObjectNode textData = objectMapper.createObjectNode();
        textData.put("content", message);
        ObjectNode postData = generatePostData(MessageType.TEXT, textData, chatId);
        sendHttpRequest(postData.toString());
    }

    /**
     * 机器人发送MarkDown
     *
     * @param chatId          群ID
     * @param markdownMessage md文本消息，按照官网要求的md格式拼接
     * @throws Exception
     */
    public void pushMarkdown(String chatId, String markdownMessage) throws Exception {
        ObjectNode textData = objectMapper.createObjectNode();
        textData.put("content", markdownMessage);
        ObjectNode postData = generatePostData(MessageType.MARKDOWN, textData, chatId);
        sendHttpRequest(postData.toString());
    }

    /**
     * 使用HttpClientUtils发送HTTP请求
     *
     * @param jsonBody JSON请求体
     * @throws Exception
     */
    private void sendHttpRequest(String jsonBody) throws Exception {
        try (HttpClientUtils client = HttpClientUtils.builder().build()) {
            // 设置请求头
            HashMap<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            
            // 使用HttpClientUtils发送POST请求
            ObjectNode response = client.postAsObject(chatBotWebHookUrl, jsonBody, headers, ObjectNode.class);
            
            // 检查响应
            if (response != null && !response.get("errcode").asText().equals("0")) {
                String errMessage = String.format("推送企业微信消息失败: %s", response.toString());
                throw new Exception(errMessage);
            }
        } catch (HttpClientUtils.HttpClientException e) {
            throw new Exception("HTTP请求失败: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new Exception("发送企业微信消息时发生异常: " + e.getMessage(), e);
        }
    }

    /**
     * 生成Post请求参数
     *
     * @param msgType    消息类型
     * @param msgContent 消息json data
     * @param chatId     群ID
     * @return
     */
    private ObjectNode generatePostData(MessageType msgType, ObjectNode msgContent, String chatId) {
        ObjectNode postData = objectMapper.createObjectNode();
        postData.put("chatid", chatId);
        postData.put("msgtype", msgType.toString());
        postData.put(msgType.toString(), msgContent);
        return postData;
    }
}