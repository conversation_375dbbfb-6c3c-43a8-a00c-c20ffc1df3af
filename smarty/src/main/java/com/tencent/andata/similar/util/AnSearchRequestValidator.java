package com.tencent.andata.similar.util;

import com.tencent.andata.similar.model.req.DataItem;
import com.tencent.andata.similar.model.req.ItemFields;
import com.tencent.andata.similar.model.req.SimilarSearchRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * AnSearch请求验证工具类
 * 确保请求完全符合接口文档要求
 */
public class AnSearchRequestValidator {
    
    private static final Logger log = LoggerFactory.getLogger(AnSearchRequestValidator.class);
    
    /**
     * 验证SimilarSearchRequest是否符合接口文档要求
     */
    public static ValidationResult validateRequest(SimilarSearchRequest request) {
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 1. 验证顶层字段
        if (request.getDataset() == null || !request.getDataset().equals("similar_search")) {
            errors.add("dataset字段必须为'similar_search'");
        }
        
        if (request.getData_type() == null || (request.getData_type() != 1 && request.getData_type() != 2)) {
            errors.add("data_type字段必须为1(短文本)或2(长文本)");
        }
        
        if (request.getRebuild() == null) {
            warnings.add("rebuild字段建议设置为true或false");
        }
        
        if (request.getCover() == null) {
            warnings.add("cover字段建议设置为true或false");
        }
        
        // 2. 验证data数组
        if (request.getData() == null || request.getData().isEmpty()) {
            errors.add("data数组不能为空");
            return new ValidationResult(false, errors, warnings);
        }
        
        // 3. 验证每个DataItem
        for (int i = 0; i < request.getData().size(); i++) {
            DataItem item = request.getData().get(i);
            validateDataItem(item, i, errors, warnings);
        }
        
        boolean isValid = errors.isEmpty();
        return new ValidationResult(isValid, errors, warnings);
    }
    
    /**
     * 验证单个DataItem
     */
    private static void validateDataItem(DataItem item, int index, List<String> errors, List<String> warnings) {
        String prefix = String.format("data[%d]", index);
        
        // 验证ID字段
        if (item.getID() == null || item.getID().trim().isEmpty()) {
            errors.add(prefix + ".ID字段不能为空");
        } else {
            // 验证ID格式: source_name-source_type-source_id
            String[] parts = item.getID().split("-");
            if (parts.length != 3) {
                errors.add(prefix + ".ID字段格式错误，应为'source_name-source_type-source_id'");
            } else {
                if (!"similar_search".equals(parts[0])) {
                    errors.add(prefix + ".ID字段第一部分应为'similar_search'");
                }
                if (!"ticket".equals(parts[1]) && !"similarity_ticket".equals(parts[1])) {
                    errors.add(prefix + ".ID字段第二部分应为'ticket'或'similarity_ticket'");
                }
            }
        }
        
        // 验证doc字段
        if (item.getDoc() == null || item.getDoc().trim().isEmpty()) {
            errors.add(prefix + ".doc字段不能为空，应为问题描述");
        }
        
        // 验证deleted字段
        if (item.getDeleted() == null || (item.getDeleted() != 0 && item.getDeleted() != 1)) {
            errors.add(prefix + ".deleted字段必须为0(有效)或1(软删除)");
        }
        
        // 验证create_time字段
        if (item.getCreate_time() == null || item.getCreate_time() <= 0) {
            errors.add(prefix + ".create_time字段必须为正整数时间戳(秒级)");
        }
        
        // 验证fields字段
        if (item.getFields() == null) {
            errors.add(prefix + ".fields字段不能为空");
        } else {
            validateItemFields(item.getFields(), prefix + ".fields", errors, warnings);
        }
    }
    
    /**
     * 验证ItemFields
     */
    private static void validateItemFields(ItemFields fields, String prefix, List<String> errors, List<String> warnings) {
        // 必需字段验证
        if (fields.getSource_name() == null || !fields.getSource_name().equals("similar_search")) {
            errors.add(prefix + ".source_name必须为'similar_search'");
        }
        
        if (fields.getSource_type() == null || 
            (!fields.getSource_type().equals("ticket") && !fields.getSource_type().equals("similarity_ticket"))) {
            errors.add(prefix + ".source_type必须为'ticket'或'similarity_ticket'");
        }
        
        if (fields.getSource_id() == null || fields.getSource_id().trim().isEmpty()) {
            errors.add(prefix + ".source_id不能为空");
        }
        
        if (fields.getTitle() == null || fields.getTitle().trim().isEmpty()) {
            errors.add(prefix + ".title不能为空，应为问题描述");
        }
        
        if (fields.getContent() == null || fields.getContent().trim().isEmpty()) {
            errors.add(prefix + ".content不能为空，应为解决方案");
        }
        
        // 验证knowledge字段
        if (fields.getKnowledge() == null || fields.getKnowledge().trim().isEmpty()) {
            errors.add(prefix + ".knowledge不能为空，应为title+\\n+content");
        } else {
            // 验证knowledge格式
            String expectedKnowledge = (fields.getTitle() != null ? fields.getTitle() : "") + 
                                     "\n" + 
                                     (fields.getContent() != null ? fields.getContent() : "");
            if (!expectedKnowledge.equals(fields.getKnowledge())) {
                warnings.add(prefix + ".knowledge字段格式可能不正确，应为title+\\n+content");
            }
        }
        
        // 业务字段验证
        if (fields.getCloud_type() == null || 
            (fields.getCloud_type() != 1 && fields.getCloud_type() != 2 && fields.getCloud_type() != 3)) {
            errors.add(prefix + ".cloud_type必须为1(私有云)、2(公有云)或3(全部)");
        }
        
        if (fields.getVisible() == null || 
            (fields.getVisible() != 1 && fields.getVisible() != 2 && fields.getVisible() != 3)) {
            errors.add(prefix + ".visible必须为1(内部)、2(外部)或3(全部)");
        }
        
        if (fields.getIndex_type() == null || !fields.getIndex_type().equals("qa")) {
            errors.add(prefix + ".index_type必须为'qa'");
        }
        
        // 时间字段验证
        if (fields.getTicket_create_time() == null || fields.getTicket_create_time() <= 0) {
            errors.add(prefix + ".ticket_create_time必须为正整数时间戳(秒级)");
        }
        
        if (fields.getTicket_close_time() == null || fields.getTicket_close_time() <= 0) {
            errors.add(prefix + ".ticket_close_time必须为正整数时间戳(秒级)");
        }
        
        // 可选字段提醒
        if (fields.getCustomer_uin() == null || fields.getCustomer_uin().trim().isEmpty()) {
            warnings.add(prefix + ".customer_uin字段为空，建议填写客户UIN");
        }
        
        if (fields.getParent_ticket_id() == null || fields.getParent_ticket_id().trim().isEmpty()) {
            warnings.add(prefix + ".parent_ticket_id字段为空，建议填写关联工单ID");
        }
    }
    
    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final List<String> errors;
        private final List<String> warnings;
        
        public ValidationResult(boolean valid, List<String> errors, List<String> warnings) {
            this.valid = valid;
            this.errors = errors;
            this.warnings = warnings;
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public List<String> getErrors() {
            return errors;
        }
        
        public List<String> getWarnings() {
            return warnings;
        }
        
        public void logResults() {
            if (!errors.isEmpty()) {
                log.error("AnSearch请求验证失败，错误信息:");
                errors.forEach(error -> log.error("  - {}", error));
            }
            
            if (!warnings.isEmpty()) {
                log.warn("AnSearch请求验证警告:");
                warnings.forEach(warning -> log.warn("  - {}", warning));
            }
            
            if (valid && warnings.isEmpty()) {
                log.info("AnSearch请求验证通过，完全符合接口文档要求");
            }
        }
    }
}