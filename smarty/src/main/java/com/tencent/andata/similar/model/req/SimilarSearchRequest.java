package com.tencent.andata.similar.model.req;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import lombok.NoArgsConstructor;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SimilarSearchRequest {
    private String dataset = "similar_search";
    private Integer data_type = 1; // 1:短文本 2:长文本
    private Boolean rebuild = true;
    private Boolean cover = true;
    private List<DataItem> data;
    private transient String request_id; // 不序列化

    // 便捷构建方法
    public static SimilarSearchRequest create(List<DataItem> items) {
        SimilarSearchRequest request = new SimilarSearchRequest();
        request.setData(items);
        return request;
    }
}