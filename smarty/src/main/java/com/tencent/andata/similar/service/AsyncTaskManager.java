package com.tencent.andata.similar.service;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.similar.model.req.SimilarSearchRequest;
import com.tencent.andata.utils.HttpClientUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

public class AsyncTaskManager {

    private static final Logger log = LoggerFactory.getLogger(AsyncTaskManager.class);

    private FlinkLog zhiyanlog;
    private final ExecutorService asyncExecutor;
    private final ObjectMapper objectMapper;

    public AsyncTaskManager(int threadPoolSize) {
        this.asyncExecutor = Executors.newFixedThreadPool(threadPoolSize);
        this.objectMapper = new ObjectMapper();
        this.zhiyanlog = FlinkLog.getInstance();
    }

    /**
     * 异步提交AnSearch任务
     */
    public void submitAnswerSearchTask(SimilarSearchRequest searchRequest,
                                     HttpClientUtils writeClient,
                                     String addUrl,
                                     String ticketId) {
        CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                String requestBody = objectMapper.writeValueAsString(searchRequest);

                // 详细记录请求参数
                String format = "[AnSearch请求] ticketId=" + ticketId + ", url=" + addUrl + ", requestId=" + searchRequest.getRequest_id() + ", requestBody=" + requestBody;
                log.info(format);
                zhiyanlog.info(format);

                // 验证序列化结果
                JsonNode bodyJson = objectMapper.readTree(requestBody);
                if (!bodyJson.has("dataset") || !bodyJson.has("data_type") || !bodyJson.has("data")) {
                    log.error("[AnSearch请求失败] ticketId={}, 缺少必要字段: dataset={}, data_type={}, data={}",
                            ticketId, bodyJson.has("dataset"), bodyJson.has("data_type"), bodyJson.has("data"));
                    return;
                }

                Map<String, String> headers = new HashMap<>();
                headers.put("request-id", searchRequest.getRequest_id());

                // 记录请求发送
                log.info("[AnSearch发送] ticketId={}, 开始发送请求到ansearch", ticketId);

                String response = writeClient.post(addUrl, requestBody, headers);
                long endTime = System.currentTimeMillis();

                // 详细记录响应结果
                log.info("[AnSearch成功] ticketId={}, 耗时={}ms, 响应内容={}",
                        ticketId, (endTime - startTime), response);

            } catch (Exception e) {
                long endTime = System.currentTimeMillis();
                log.error("[AnSearch失败] ticketId={}, 耗时={}ms, 错误信息={}, 异常详情:",
                        ticketId, (endTime - startTime), e.getMessage(), e);
                // 可以在这里添加重试逻辑或错误统计
            }
        }, asyncExecutor);
    }

    /**
     * 异步提交通用任务
     */
    public <T> CompletableFuture<T> submitTask(java.util.function.Supplier<T> task) {
        return CompletableFuture.supplyAsync(task, asyncExecutor);
    }

    /**
     * 异步提交无返回值任务
     */
    public CompletableFuture<Void> submitVoidTask(Runnable task) {
        return CompletableFuture.runAsync(task, asyncExecutor);
    }

    /**
     * 优雅关闭线程池
     */
    public void shutdown() {
        log.info("Shutting down async task manager...");
        asyncExecutor.shutdown();
        try {
            if (!asyncExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                log.warn("Async executor did not terminate gracefully, forcing shutdown...");
                asyncExecutor.shutdownNow();
                if (!asyncExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    log.error("Async executor did not terminate");
                }
            }
        } catch (InterruptedException e) {
            log.warn("Interrupted while waiting for async executor to terminate");
            asyncExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("Async task manager shut down completed.");
    }

    /**
     * 检查线程池状态
     */
    public boolean isShutdown() {
        return asyncExecutor.isShutdown();
    }

    /**
     * 检查线程池是否终止
     */
    public boolean isTerminated() {
        return asyncExecutor.isTerminated();
    }
}