package com.tencent.andata.similar.util;

import static com.tencent.andata.similar.util.BusinessUtils.parseTimestampString;

import java.math.BigDecimal;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import java.util.Iterator;
import java.util.Map;

public class JsonMergeUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static String formatLargeNumber(JsonNode node) {
        if (node == null) {
            return "";
        }
        if (node.isTextual()) {
            String text = node.asText();
            // 如果包含科学计数法，转换为普通数字格式
            if (text.contains("E") || text.contains("e")) {
                try {
                    BigDecimal bd = new BigDecimal(text);
                    return bd.toPlainString();
                } catch (NumberFormatException e) {
                    return text;
                }
            }
            return text;
        }
        return String.valueOf(node.asLong());
    }

    public static String getJsonFieldSafely(JsonNode jsonNode, String fieldName, String defaultValue) {
        if (jsonNode == null || jsonNode.isNull()) {
            return defaultValue;
        }

        JsonNode fieldNode = jsonNode.get(fieldName);
        if (fieldNode == null || fieldNode.isNull()) {
            return defaultValue;
        }

        String value = fieldNode.asText();
        return (value == null || value.trim().isEmpty()) ? defaultValue : value;
    }

    public static Long getLongValue(JsonNode jsonNode, String fieldName, Long defaultValue) {
        try {
            if (jsonNode == null || fieldName == null) {
                return defaultValue;
            }
            JsonNode field = jsonNode.get(fieldName);
            if (field == null || field.isNull() || field.isMissingNode()) {
                return defaultValue;
            }
            String value = field.asText();
            if (value == null || value.trim().isEmpty()) {
                return defaultValue;
            }

            // 尝试直接解析为Long
            try {
                return Long.valueOf(value.trim());
            } catch (NumberFormatException e) {
                // 如果直接解析失败，尝试作为时间戳字符串解析
                return parseTimestampString(value.trim(), defaultValue);
            }
        } catch (Exception e) {

            return defaultValue;
        }
    }

    public static String getTextValue(JsonNode jsonNode, String fieldName) {
        return getTextValue(jsonNode, fieldName, null);
    }

    public static String getTextValue(JsonNode jsonNode, String fieldName, String defaultValue) {
        try {
            if (jsonNode == null || fieldName == null) {
                return defaultValue;
            }
            JsonNode field = jsonNode.get(fieldName);
            if (field == null || field.isNull() || field.isMissingNode()) {
                return defaultValue;
            }
            String value = field.asText();
            return value != null ? value : defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static JsonNode addStringField(JsonNode jsonNode, String fieldName, String fieldValue,
            ObjectMapper mapper) {
        if (jsonNode == null || fieldName == null || fieldValue == null || mapper == null) {
            return jsonNode;
        }

        if (jsonNode.isObject()) {
            ((ObjectNode) jsonNode).put(fieldName, fieldValue);
            return jsonNode;
        } else {
            ObjectNode newJsonNode = mapper.createObjectNode();
            if (jsonNode.isObject()) {
                newJsonNode.setAll((ObjectNode) jsonNode);
            }
            newJsonNode.put(fieldName, fieldValue);
            return newJsonNode;
        }
    }

    /**
     * 简单合并：第二个JSON的字段会覆盖第一个JSON的同名字段
     *
     * @param jsonA 第一个JSON对象
     * @param jsonB 第二个JSON对象
     * @return 合并后的JsonNode
     */
    public static JsonNode mergeJsonSimple(JsonNode jsonA, JsonNode jsonB) {
        if (jsonA == null) {
            return jsonB;
        }
        if (jsonB == null) {
            return jsonA;
        }

        // 创建一个新的ObjectNode来存储结果
        ObjectNode result = objectMapper.createObjectNode();

        // 先添加jsonA的所有字段
        if (jsonA.isObject()) {
            result.setAll((ObjectNode) jsonA);
        }

        // 然后添加jsonB的字段（会覆盖同名字段）
        if (jsonB.isObject()) {
            result.setAll((ObjectNode) jsonB);
        }

        return result;
    }

    /**
     * 深度合并：递归合并嵌套对象，数组会被合并
     *
     * @param jsonA 第一个JSON对象
     * @param jsonB 第二个JSON对象
     * @return 合并后的JsonNode
     */
    public static JsonNode mergeJsonDeep(JsonNode jsonA, JsonNode jsonB) {
        if (jsonA == null) {
            return jsonB != null ? jsonB.deepCopy() : null;
        }
        if (jsonB == null) {
            return jsonA.deepCopy();
        }

        // 如果两个都不是对象类型，返回jsonB（后者优先）
        if (!jsonA.isObject() || !jsonB.isObject()) {
            return jsonB.deepCopy();
        }

        ObjectNode result = (ObjectNode) jsonA.deepCopy();

        // 遍历jsonB的所有字段
        Iterator<Map.Entry<String, JsonNode>> fields = jsonB.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String fieldName = entry.getKey();
            JsonNode fieldValue = entry.getValue();

            if (result.has(fieldName)) {
                // 如果字段已存在，进行深度合并
                JsonNode existingValue = result.get(fieldName);
                if (existingValue.isObject() && fieldValue.isObject()) {
                    // 递归合并对象
                    result.set(fieldName, mergeJsonDeep(existingValue, fieldValue));
                } else if (existingValue.isArray() && fieldValue.isArray()) {
                    // 合并数组
                    ArrayNode mergedArray = objectMapper.createArrayNode();
                    mergedArray.addAll((ArrayNode) existingValue);
                    mergedArray.addAll((ArrayNode) fieldValue);
                    result.set(fieldName, mergedArray);
                } else {
                    // 其他情况直接覆盖
                    result.set(fieldName, fieldValue.deepCopy());
                }
            } else {
                // 字段不存在，直接添加
                result.set(fieldName, fieldValue.deepCopy());
            }
        }

        return result;
    }

    /**
     * 安全合并：只有当字段不存在时才添加，不会覆盖已存在的字段
     *
     * @param jsonA 第一个JSON对象（优先级高）
     * @param jsonB 第二个JSON对象（作为补充）
     * @return 合并后的JsonNode
     */
    public static JsonNode mergeJsonSafe(JsonNode jsonA, JsonNode jsonB) {
        if (jsonA == null) {
            return jsonB != null ? jsonB.deepCopy() : null;
        }
        if (jsonB == null) {
            return jsonA.deepCopy();
        }

        if (!jsonA.isObject() || !jsonB.isObject()) {
            return jsonA.deepCopy(); // 优先返回第一个
        }

        ObjectNode result = (ObjectNode) jsonA.deepCopy();

        // 只添加jsonA中不存在的字段
        Iterator<Map.Entry<String, JsonNode>> fields = jsonB.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String fieldName = entry.getKey();
            JsonNode fieldValue = entry.getValue();

            if (!result.has(fieldName)) {
                result.set(fieldName, fieldValue.deepCopy());
            }
        }

        return result;
    }

    /**
     * 自定义合并：提供合并策略的回调接口
     */
    @FunctionalInterface
    public interface MergeStrategy {

        JsonNode merge(String fieldName, JsonNode valueA, JsonNode valueB);
    }

    /**
     * 使用自定义策略合并JSON
     *
     * @param jsonA 第一个JSON对象
     * @param jsonB 第二个JSON对象
     * @param strategy 合并策略
     * @return 合并后的JsonNode
     */
    public static JsonNode mergeJsonWithStrategy(JsonNode jsonA, JsonNode jsonB, MergeStrategy strategy) {
        if (jsonA == null) {
            return jsonB != null ? jsonB.deepCopy() : null;
        }
        if (jsonB == null) {
            return jsonA.deepCopy();
        }

        if (!jsonA.isObject() || !jsonB.isObject()) {
            return strategy.merge("", jsonA, jsonB);
        }

        ObjectNode result = objectMapper.createObjectNode();

        // 添加jsonA的所有字段
        Iterator<Map.Entry<String, JsonNode>> fieldsA = jsonA.fields();
        while (fieldsA.hasNext()) {
            Map.Entry<String, JsonNode> entry = fieldsA.next();
            result.set(entry.getKey(), entry.getValue().deepCopy());
        }

        // 处理jsonB的字段
        Iterator<Map.Entry<String, JsonNode>> fieldsB = jsonB.fields();
        while (fieldsB.hasNext()) {
            Map.Entry<String, JsonNode> entry = fieldsB.next();
            String fieldName = entry.getKey();
            JsonNode fieldValue = entry.getValue();

            if (result.has(fieldName)) {
                // 字段冲突，使用策略处理
                JsonNode mergedValue = strategy.merge(fieldName, result.get(fieldName), fieldValue);
                result.set(fieldName, mergedValue);
            } else {
                // 新字段，直接添加
                result.set(fieldName, fieldValue.deepCopy());
            }
        }

        return result;
    }

    // 测试和使用示例
    public static void main(String[] args) throws Exception {
        ObjectMapper mapper = new ObjectMapper();

        // 创建测试JSON
        String jsonAStr = "{\"field1\":1,\"field2\":\"2\"}";
        String jsonBStr = "{\"field3\":{\"a\":\"b\"},\"field4\":4}";

        JsonNode jsonA = mapper.readTree(jsonAStr);
        JsonNode jsonB = mapper.readTree(jsonBStr);

        System.out.println("原始JSON A: " + jsonA.toString());
        System.out.println("原始JSON B: " + jsonB.toString());

        // 1. 简单合并
        JsonNode simpleResult = mergeJsonSimple(jsonA, jsonB);
        System.out.println("简单合并结果: " + simpleResult.toString());

        // 2. 深度合并
        JsonNode deepResult = mergeJsonDeep(jsonA, jsonB);
        System.out.println("深度合并结果: " + deepResult.toString());

        // 3. 安全合并
        JsonNode safeResult = mergeJsonSafe(jsonA, jsonB);
        System.out.println("安全合并结果: " + safeResult.toString());

        // 4. 自定义策略合并示例
        JsonNode customResult = mergeJsonWithStrategy(jsonA, jsonB, (fieldName, valueA, valueB) -> {
            // 自定义策略：如果字段名包含"field"，则优先使用valueA，否则使用valueB
            if (fieldName.contains("field")) {
                return valueA;
            }
            return valueB;
        });
        System.out.println("自定义策略合并结果: " + customResult.toString());

        // 测试嵌套对象合并
        testNestedMerge();
    }

    private static void testNestedMerge() throws Exception {
        ObjectMapper mapper = new ObjectMapper();

        String nestedA = "{\"user\":{\"name\":\"Alice\",\"age\":25},\"config\":{\"theme\":\"dark\"}}";
        String nestedB = "{\"user\":{\"email\":\"<EMAIL>\",\"age\":26},\"config\":{\"language\":\"en\"}}";

        JsonNode jsonA = mapper.readTree(nestedA);
        JsonNode jsonB = mapper.readTree(nestedB);

        System.out.println("\n=== 嵌套对象合并测试 ===");
        System.out.println("嵌套JSON A: " + jsonA.toString());
        System.out.println("嵌套JSON B: " + jsonB.toString());

        JsonNode deepMerged = mergeJsonDeep(jsonA, jsonB);
        System.out.println("深度合并结果: " + deepMerged.toString());
        // 期望结果：{"user":{"name":"Alice","age":26,"email":"<EMAIL>"},"config":{"theme":"dark","language":"en"}}
    }
}